{"$schema": "https://schema.tauri.app/config/2", "productName": "telex", "version": "0.1.0", "identifier": "com.mac.telex", "build": {"beforeDevCommand": "pnpm dev", "devUrl": "http://localhost:3000/client/test-notifications", "beforeBuildCommand": "pnpm build", "frontendDist": "https://telex.im/"}, "app": {"windows": [{"title": "telex", "width": 800, "height": 600}], "security": {"csp": null}}, "bundle": {"active": true, "targets": "all", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"]}, "plugins": {"notification": {"all": "true"}}}